import { Suspense } from "react"
import { redirect } from "next/navigation"

import SearchPageSkeleton from "@/app/components/search/SearchPageSkeleton"
import { SearchFilters } from "@/types/search"
import { getSearchPageData } from "@/app/getData/search"
import SearchPageContent from "@/app/components/search/SearchPageContent"
import { getCurrentDbUser } from "@/lib/auth"
import { getPretSiStocBatch, getPricesFor4thBatch } from "@/lib/mssql/query"
import { ProductCardInterface } from "@/types/product"

interface SearchPageProps {
  searchParams: {
    query?: string
    category3?: string
    brands?: string | string[]
    classes?: string | string[]
    minPrice?: string
    maxPrice?: string
    hasDiscount?: string
    page?: string
    sort?: 'price_asc' | 'price_desc' | 'discount_desc' | 'name_asc' | 'relevance'
  }
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
  // 1️⃣ Auth
  const user = await getCurrentDbUser()
  if (!user) {
    return redirect("/sign-in")
  }

  // Parse search params into filters
  const filters: SearchFilters = {
    query: searchParams.query,
    category3: searchParams.category3,
    brands: Array.isArray(searchParams.brands)
      ? searchParams.brands
      : searchParams.brands?.split(',').filter(Boolean) || [],
    classes: Array.isArray(searchParams.classes)
      ? searchParams.classes
      : searchParams.classes?.split(',').filter(Boolean) || [],
    minPrice: searchParams.minPrice ? parseFloat(searchParams.minPrice) : undefined,
    maxPrice: searchParams.maxPrice ? parseFloat(searchParams.maxPrice) : undefined,
    hasDiscount: searchParams.hasDiscount === 'true',
    page: searchParams.page ? parseInt(searchParams.page) : 1,
    sort: searchParams.sort || 'relevance'
  }

  // 2️⃣ Get search data
  const searchData = await getSearchPageData(filters)

  // 3️⃣ Determine if user gets special 4️⃣-level pricing
  const has4th = user.role.includes("fourLvlAdminAB") || user.role.includes("fourLvlInregistratAB")

  // 4️⃣ Prepare SKU list from products
  const productSkus = searchData.products.map((p) => p.Material_Number)

  // 5️⃣ Batch-load stock & conditional 4th-level pricing
  const [stockMap, price4Batch] = await Promise.all([
    getPretSiStocBatch(productSkus),
    has4th ? getPricesFor4thBatch(productSkus, user.userAM || "") : Promise.resolve([])
  ])

  // 6️⃣ Build price4Map for easy lookup
  const price4Map = new Map(price4Batch.map((p) => [p.itemno, p.pret]))

  // 7️⃣ Merge everything into enriched products
  const enrichedProducts: ProductCardInterface[] = searchData.products.map((product) => {
    const code = product.Material_Number
    const batch = stockMap[code] ?? []

    // sum stock across all locations
    const stock = batch.reduce((sum, e) => sum + e.stoc, 0)

    // if user has 4th-level role and price exists, use it; else fall back
    const displayPrice = price4Map.get(code) ?? product.FinalPrice

    return {
      ...product,
      stock,
      displayPrice,
    }
  })

  // 8️⃣ Adjust price range for role-based pricing if needed
  let adjustedPriceRange = searchData.priceRange

  if (has4th && price4Batch.length > 0) {
    // If user has 4th level pricing, we need to adjust the range
    // For simplicity, we'll use the database range but apply a general adjustment
    // This is an approximation since we can't easily get all 4th level prices
    const price4Values = price4Batch.map(p => p.pret)
    const dbPrices = searchData.products.map(p => p.FinalPrice).filter(p => p !== null) as number[]

    if (price4Values.length > 0 && dbPrices.length > 0) {
      // Calculate average discount ratio from 4th level pricing
      const avgRatio = price4Values.reduce((sum, p4Price, index) => {
        const dbPrice = dbPrices[index]
        return sum + (dbPrice ? p4Price / dbPrice : 1)
      }, 0) / price4Values.length

      adjustedPriceRange = {
        min: Math.round(searchData.priceRange.min * avgRatio * 100) / 100,
        max: Math.round(searchData.priceRange.max * avgRatio * 100) / 100
      }
    }
  }

  // 9️⃣ Update search data with enriched products and adjusted price range
  const enrichedSearchData = {
    ...searchData,
    products: enrichedProducts,
    priceRange: adjustedPriceRange
  }

  return (
    <div className="min-h-screen ">
      <div className="max-w-[1640px] mx-auto px-4 py-6">
        <SearchPageContent searchData={enrichedSearchData} />
      </div>
    </div>
  )
}

// Wrapper component to handle async data fetching
// async function SearchPageContentWrapper({ filters }: { filters: SearchFilters }) {
//   const searchData = await getSearchPageData(filters)

//   return <SearchPageContent searchData={searchData} />
// }