import { Suspense } from "react"

import SearchPageSkeleton from "@/app/components/search/SearchPageSkeleton"
import { SearchFilters } from "@/types/search"
import { getSearchPageData } from "@/app/getData/search"
import SearchPageContent from "@/app/components/search/SearchPageContent"

interface SearchPageProps {
  searchParams: {
    query?: string
    category3?: string
    brands?: string | string[]
    classes?: string | string[]
    minPrice?: string
    maxPrice?: string
    hasDiscount?: string
    page?: string
    sort?: 'price_asc' | 'price_desc' | 'discount_desc' | 'name_asc' | 'relevance'
  }
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
  // Parse search params into filters
  const filters: SearchFilters = {
    query: searchParams.query,
    category3: searchParams.category3,
    brands: Array.isArray(searchParams.brands)
      ? searchParams.brands
      : searchParams.brands?.split(',').filter(Boolean) || [],
    classes: Array.isArray(searchParams.classes)
      ? searchParams.classes
      : searchParams.classes?.split(',').filter(Boolean) || [],
    minPrice: searchParams.minPrice ? parseFloat(searchParams.minPrice) : undefined,
    maxPrice: searchParams.maxPrice ? parseFloat(searchParams.maxPrice) : undefined,
    hasDiscount: searchParams.hasDiscount === 'true',
    page: searchParams.page ? parseInt(searchParams.page) : 1,
    sort: searchParams.sort || 'relevance'
  }

  const searchData = await getSearchPageData(filters)

  return (
    <div className="min-h-screen ">
      <div className="max-w-[1640px] mx-auto px-4 py-6">
        {/* <Suspense fallback={<SearchPageSkeleton />}>
          <SearchPageContentWrapper filters={filters} />
        </Suspense> */}
          <SearchPageContent searchData={searchData} /> 
      </div>
    </div>
  )
}

// Wrapper component to handle async data fetching
// async function SearchPageContentWrapper({ filters }: { filters: SearchFilters }) {
//   const searchData = await getSearchPageData(filters)

//   return <SearchPageContent searchData={searchData} />
// }