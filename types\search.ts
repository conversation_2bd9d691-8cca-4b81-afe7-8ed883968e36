
import type { DiscountType as PrismaDiscountType } from "@/generated/prisma";
import { ProductCardInterface } from "./product";

export interface SearchSuggestion {
  Material_Number: string
  Description_Local: string | null
  ImageUrl: string[]
  FinalPrice: number | null
  PretAM: number | null
  HasDiscount: boolean
  discountPercentage: number | null
  activeDiscountType: PrismaDiscountType | null;
  activeDiscountValue: number | null;
}

export interface SearchSuggestionsResult {
  products: SearchSuggestion[]
  firstCategory: {
    id: string
    name: string
    slug: string | null
  } | null
}

// Search page interfaces
export interface SearchFilters {
  query?: string
  category3?: string
  brands?: string[]
  classes?: string[]
  minPrice?: number
  maxPrice?: number
  hasDiscount?: boolean
  page?: number
  sort?: 'price_asc' | 'price_desc' | 'discount_desc' | 'name_asc' | 'relevance'
}

export interface SearchPageData {
  products: ProductCardInterface[]
  categories: Array<{
    id: string
    name: string
    slug: string | null
    productCount: number
  }>
  brands: Array<{
    id: string
    name: string
    productCount: number
  }>
  classes: Array<{
    id: string
    name: string
    productCount: number
    brandId?: string
    originalClassId?: string
  }>
  priceRange: {
    min: number
    max: number
  }
  pagination: {
    total: number
    pages: number
    currentPage: number
    hasNext: boolean
    hasPrev: boolean
  }
  appliedFilters: SearchFilters
}