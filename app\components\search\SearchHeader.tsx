"use client"

import { useRouter, useSearchParams } from "next/navigation"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"
import { SearchFilters } from "@/types/search"

interface SearchHeaderProps {
  query?: string
  totalResults: number
  appliedFilters: SearchFilters
}

export default function SearchHeader({ query, totalResults, appliedFilters }: SearchHeaderProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const handleSortChange = (sort: string) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('sort', sort)
    params.delete('page') // Reset to first page when sorting
    router.push(`/search?${params.toString()}`)
  }

  const removeFilter = (filterType: string, value?: string) => {
    const params = new URLSearchParams(searchParams.toString())
    
    switch (filterType) {
      case 'query':
        params.delete('query')
        break
      case 'category3':
        params.delete('category3')
        break
      case 'brand':
        if (value) {
          const brands = params.get('brands')?.split(',').filter(b => b !== value) || []
          if (brands.length > 0) {
            params.set('brands', brands.join(','))
          } else {
            params.delete('brands')
          }
        }
        break
      case 'class':
        if (value) {
          const classes = params.get('classes')?.split(',').filter(c => c !== value) || []
          if (classes.length > 0) {
            params.set('classes', classes.join(','))
          } else {
            params.delete('classes')
          }
        }
        break
      case 'price':
        params.delete('minPrice')
        params.delete('maxPrice')
        break
      case 'discount':
        params.delete('hasDiscount')
        break
    }
    
    params.delete('page') // Reset to first page
    router.push(`/search?${params.toString()}`)
  }

  const sortOptions = [
    { value: 'relevance', label: 'Relevanta' },
    { value: 'price_asc', label: 'Pret crescator' },
    { value: 'price_desc', label: 'Pret descrescator' },
    { value: 'discount_desc', label: 'Discount mare' },
    { value: 'name_asc', label: 'Nume A-Z' }
  ]

  return (
    <div className="mb-6">
      {/* Results count and sort */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {query ? `Rezultate pentru "${query}"` : 'Rezultate cautare'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {totalResults.toLocaleString('ro-RO')} produse gasite
          </p>
        </div>

        <Select value={appliedFilters.sort || 'relevance'} onValueChange={handleSortChange}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Sorteaza dupa" />
          </SelectTrigger>
          <SelectContent>
            {sortOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Applied filters */}
      <div className="flex flex-wrap gap-2">
        {query && (
          <Badge variant="secondary" className="flex items-center gap-1">
            Cautare: {query}
            <X 
              className="h-3 w-3 cursor-pointer hover:text-red-500" 
              onClick={() => removeFilter('query')}
            />
          </Badge>
        )}

        {appliedFilters.brands?.map(brand => (
          <Badge key={brand} variant="secondary" className="flex items-center gap-1">
            Brand: {brand}
            <X 
              className="h-3 w-3 cursor-pointer hover:text-red-500" 
              onClick={() => removeFilter('brand', brand)}
            />
          </Badge>
        ))}

        {appliedFilters.classes?.map(cls => (
          <Badge key={cls} variant="secondary" className="flex items-center gap-1">
            Clasa: {cls}
            <X 
              className="h-3 w-3 cursor-pointer hover:text-red-500" 
              onClick={() => removeFilter('class', cls)}
            />
          </Badge>
        ))}

        {(appliedFilters.minPrice || appliedFilters.maxPrice) && (
          <Badge variant="secondary" className="flex items-center gap-1">
            Pret: {appliedFilters.minPrice || 0} - {appliedFilters.maxPrice || '∞'} RON
            <X 
              className="h-3 w-3 cursor-pointer hover:text-red-500" 
              onClick={() => removeFilter('price')}
            />
          </Badge>
        )}

        {appliedFilters.hasDiscount && (
          <Badge variant="secondary" className="flex items-center gap-1">
            Cu discount
            <X 
              className="h-3 w-3 cursor-pointer hover:text-red-500" 
              onClick={() => removeFilter('discount')}
            />
          </Badge>
        )}
      </div>
    </div>
  )
}
