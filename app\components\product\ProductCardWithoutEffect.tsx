"use client";

import { motion } from "framer-motion";
import { cn, formatDiscount, formatPriceRON, getStockStatus } from "@/lib/utils";
import { ProductCardInterface } from "@/types/product";
import Image from "next/image";
import WishlistButton from "../wishlist/WishlistButton";
import CartButtonProduct from "../cart/CartButtonProduct";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";

export function ProductCardWithoutEffect({ product, index }: { product: ProductCardInterface; index: number }) {
     const stockStatus = getStockStatus(product.stock || 0);
    return(
        <>
                <div
                  key={index}
                  className="group/card relative rounded-lg border dark:border-gray-700 overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 flex flex-col h-full"
                >
                  <div className="relative aspect-w-4 aspect-h-3 h-48">
                    <Link href={`/product/${product.Material_Number}`}>
                      <Image
                        priority
                        src={product.ImageUrl[0] || "https://op47vimj99.ufs.sh/f/6Hnm5nafTbm9yw7hTO5p9dqwM0gSzW5riAs8G7cYPaytnUOu"}
                        alt={product.Description_Local || ""}
                        width={500}
                        height={500}
                        className="w-full h-full object-cover transform group-hover/card:scale-105 transition-transform duration-300"
                      />
                    </Link>
                    {product.HasDiscount && product.activeDiscountType && product.activeDiscountValue && (
                      <div className="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                        <span>{formatDiscount(product.activeDiscountType, product.activeDiscountValue)}</span>
                      </div>
                    )}
                    <div className="absolute top-2 right-2 space-y-2">
                        <WishlistButton material_number={product.Material_Number} />
                    </div>
                  </div>

                  
                  <div className="p-4 flex-grow flex flex-col">
                    <Link href={`/product/${product.Material_Number}`}>
                      <div className="text-sm  mb-1">
                        {product.categoryLevel3?.name}
                      </div>
                      <h3 className="text-lg font-semibold  mb-1 line-clamp-2">
                        {product.Description_Local}
                      </h3>
                      <div className="text-xs  mb-2 flex-grow">
                        {product.productClass?.vehicleModels && (
                          <p className="mb-1 font-medium line-clamp-1">Compatibil cu: {product.productClass.vehicleModels.map(vm => vm.vehicleModel.name).join("/")}</p>
                        )}
                      </div>
                    </Link>

                    <div className="flex items-center justify-between mt-auto">
                      <div className="flex flex-col">
                        {product.HasDiscount ? (
                          <span className="text-xs text-gray-400 line-through">
                            {formatPriceRON(product.PretAM)}
                          </span>
                        ) : <br></br>}
                        <span className="text-md text-red-500 font-bold ">
                          {formatPriceRON(product.displayPrice || product.FinalPrice)}
                        </span>
                      </div>
                      {product.displayPrice ? (
                        <CartButtonProduct product={product.Material_Number} />
                      ) : (
                        <Button
                          disabled 
                          size="icon" 
                          className="p-2 rounded-full bg-[#0066B1] hover:bg-[#004d85] text-white shadow-sm transition-colors"
                        >
                          <X className="w-5 h-5" color="red"/>
                        </Button>
                      )}
                    </div>

                    <div className="mt-3 flex items-center justify-between text-sm">
                      <div className="text-xs font-medium px-2 py-1 bg-gray-100 dark:bg-gray-400 dark:text-black rounded-md">
                        OE: {product.Material_Number}
                      </div>
                    <span
                      className={cn(
                        "px-2 py-1 rounded-full text-xs",
                        stockStatus === "in stoc"
                          ? "bg-green-100 text-green-800"
                          : stockStatus === "stoc critic"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-red-100 text-red-800",
                      )}
                    >
                      {stockStatus}
                    </span>                        
                    </div>
                  </div>
                </div>
        </>
    )
}   




                {/* <div className="relative aspect-w-4 aspect-h-3">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover transform group-hover/card:scale-105 transition-transform duration-300"
                  />
                  {product.isOnSale && (
                    <Badge className="absolute top-2 left-2 bg-red-500">
                      Sale
                    </Badge>
                  )}
                  <div className="absolute top-2 right-2 space-y-2">
                    <button className="p-2 rounded-full bg-white/90 hover:bg-white shadow-sm transition-colors">
                      <Heart className="w-5 h-5 text-gray-600 hover:text-red-500 transition-colors" />
                    </button>
                  </div>
                </div>

                <div className="p-4">
                  <div className="text-sm text-gray-500 mb-1">
                    {product.category}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {product.name}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                    {product.description}
                  </p>

                  <div className="flex items-center justify-between mt-4">
                    <div className="flex items-baseline gap-2">
                      <span className="text-xl font-bold text-gray-900">
                        ${product.price.toLocaleString()}
                      </span>
                      {product.originalPrice && (
                        <span className="text-sm text-gray-500 line-through">
                          ${product.originalPrice.toLocaleString()}
                        </span>
                      )}
                    </div>
                    <button className="p-2 rounded-full bg-[#0066B1] hover:bg-[#004d85] text-white shadow-sm transition-colors">
                      <ShoppingCart className="w-5 h-5" />
                    </button>
                  </div>

                  <div className="mt-3 flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <div className="text-xs font-medium px-2 py-1 bg-gray-100 rounded-md text-gray-700">
                        OE:{" "}
                        {product.id === "m-sport-brake-kit"
                          ? "34112450562"
                          : product.id === "kw-v3-coilovers"
                            ? "31302450678"
                            : "13717450509"}
                      </div>
                    </div>
                    <span
                      className={cn(
                        "px-2 py-1 rounded-full text-xs",
                        product.stockStatus === "In Stock"
                          ? "bg-green-100 text-green-800"
                          : product.stockStatus === "Low Stock"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-red-100 text-red-800",
                      )}
                    >
                      {product.stockStatus}
                    </span>
                  </div>
                </div> */}