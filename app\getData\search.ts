"server-only"

import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { toSafeNumber } from "@/lib/utils";
import { SearchFilters, SearchPageData, SearchSuggestionsResult } from "@/types/search";
import { ProductCardInterface } from "@/types/product";



// Get search suggestions (max 5 products + first relevant category)
export async function getSearchSuggestions(query: string): Promise<SearchSuggestionsResult> {
  console.log("getSearchSuggestions called");
  if (!query || query.length < 3) {
    return { products: [], firstCategory: null }
  }

  try {
    // Search for products
    const products = await withRetry(() => prisma.product.findMany({
      where: {
        OR: [
          { Material_Number: { contains: query.toLowerCase() } },
          { Description_Local: { contains: query.toLowerCase() } }
        ],
        isActive: true
      },
      take: 5,
      select: {
        Material_Number: true,
        Description_Local: true,
        ImageUrl: true,
        FinalPrice: true,
        PretAM: true,
        HasDiscount: true,
        activeDiscountType: true,
        activeDiscountValue: true,
        discountPercentage: true,
        categoryLevel3: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      },
      orderBy: [
        { HasDiscount: 'desc' }, // Prioritize discounted products
        { FinalPrice: 'asc' }
      ]
    }))

    // Find first relevant category from the products found
    const firstCategory = products.length > 0 && products[0].categoryLevel3
      ? {
          id: products[0].categoryLevel3.id,
          name: products[0].categoryLevel3.name,
          slug: products[0].categoryLevel3.slug
        }
      : null

    // Remove categoryLevel3 from products for cleaner response
    const cleanProducts = products.map(({ categoryLevel3, ...product }) => product)

    //convert cleanProducts to SearchSuggestionsResult interface, example :Decimal to number
    const searchSuggestionsResult = cleanProducts.map((product) => ({
      Material_Number: product.Material_Number,
      Description_Local: product.Description_Local,
      ImageUrl: product.ImageUrl,
      FinalPrice: toSafeNumber(product.FinalPrice) ,
      PretAM: toSafeNumber(product.PretAM),
      HasDiscount: product.HasDiscount,
      activeDiscountType: product.activeDiscountType,
      activeDiscountValue:  toSafeNumber(product.activeDiscountValue),
      discountPercentage:  toSafeNumber(product.discountPercentage)
    }))

    return {
      products: searchSuggestionsResult,
      firstCategory
    }

  } catch (error) {
    logger.error('Error fetching search suggestions:', error)
    return { products: [], firstCategory: null }
  }
}



// Get search page data with filters
export async function getSearchPageData(filters: SearchFilters): Promise<SearchPageData> {
  const {
    query = '',
    category3,
    brands = [],
    classes = [],
    minPrice,
    maxPrice,
    hasDiscount,
    page = 1,
    sort = 'relevance'
  } = filters

  const pageSize = 24
  const skip = (page - 1) * pageSize

  try {
    // Build where clause
    const whereClause: any = {
      isActive: true,
      AND: []
    }

    // Search query
    if (query && query.length >= 3) {
      whereClause.AND.push({
        OR: [
          { Material_Number: { contains: query.toLowerCase() } },
          { Description_Local: { contains: query.toLowerCase() } }
        ]
      })
    }

    // Category filter
    if (category3) {
      whereClause.AND.push({
        categoryLevel3Id: category3
      })
    }

    // Brand filter (through ProductClass)
    if (brands.length > 0) {
      whereClause.AND.push({
        productClass: {
          brand: {
            id: { in: brands }
          }
        }
      })
    }

    // Class filter
    if (classes.length > 0) {
      whereClause.AND.push({
        classId: { in: classes }
      })
    }

    // Price range filter
    if (minPrice !== undefined || maxPrice !== undefined) {
      const priceFilter: any = {}
      if (minPrice !== undefined) priceFilter.gte = minPrice
      if (maxPrice !== undefined) priceFilter.lte = maxPrice
      whereClause.AND.push({
        FinalPrice: priceFilter
      })
    }

    // Discount filter
    if (hasDiscount) {
      whereClause.AND.push({
        HasDiscount: true
      })
    }

    // Build order by clause
    let orderBy: any = []
    switch (sort) {
      case 'price_asc':
        orderBy = [{ FinalPrice: 'asc' }]
        break
      case 'price_desc':
        orderBy = [{ FinalPrice: 'desc' }]
        break
      case 'discount_desc':
        orderBy = [{ discountPercentage: 'desc' }, { FinalPrice: 'asc' }]
        break
      case 'name_asc':
        orderBy = [{ Description_Local: 'asc' }]
        break
      case 'relevance':
      default:
        orderBy = [
          { HasDiscount: 'desc' },
          { FinalPrice: 'asc' }
        ]
        break
    }

    // Execute queries in parallel
    const [products, totalCount, categories, brands_data, classes_data, priceRange] = await Promise.all([
      // Products
      withRetry(() => prisma.product.findMany({
        where: whereClause,
        select: {
          id: true,
          Material_Number: true,
          Description_Local: true,
          ImageUrl: true,
          FinalPrice: true,
          PretAM: true,
          HasDiscount: true,
          activeDiscountType: true,
          activeDiscountValue: true,
          discountPercentage: true,
          categoryLevel3: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          productClass: {
            select: {
              id: true,
              classCode: true,
              brand: {
                select: {
                  id: true,
                  name: true
                }
              },
              vehicleModels: {
                select: {
                  vehicleModel: {
                    select: {
                      name: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy,
        skip,
        take: pageSize
      })),

      // Total count
      withRetry(() => prisma.product.count({
        where: whereClause
      })),

      // Categories with counts
      withRetry(() => prisma.categoryLevel3.findMany({
        where: {
          products: {
            some: {
              isActive: true,
              ...(query && query.length >= 3 ? {
                OR: [
                  { Material_Number: { contains: query.toLowerCase() } },
                  { Description_Local: { contains: query.toLowerCase() } }
                ]
              } : {})
            }
          }
        },
        select: {
          id: true,
          name: true,
          slug: true,
          _count: {
            select: {
              products: {
                where: {
                  isActive: true,
                  ...(query && query.length >= 3 ? {
                    OR: [
                      { Material_Number: { contains: query.toLowerCase() } },
                      { Description_Local: { contains: query.toLowerCase() } }
                    ]
                  } : {})
                }
              }
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      })),

      // Brands with counts
      withRetry(() => prisma.brand.findMany({
        where: {
          productClasses: {
            some: {
              products: {
                some: {
                  isActive: true,
                  ...(query && query.length >= 3 ? {
                    OR: [
                      { Material_Number: { contains: query.toLowerCase() } },
                      { Description_Local: { contains: query.toLowerCase() } }
                    ]
                  } : {})
                }
              }
            }
          }
        },
        select: {
          id: true,
          name: true,
          _count: {
            select: {
              productClasses: {
                where: {
                  products: {
                    some: {
                      isActive: true,
                      ...(query && query.length >= 3 ? {
                        OR: [
                          { Material_Number: { contains: query.toLowerCase() } },
                          { Description_Local: { contains: query.toLowerCase() } }
                        ]
                      } : {})
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      })),

      // Classes with counts
      withRetry(() => prisma.productClass.findMany({
        where: {
          products: {
            some: {
              isActive: true,
              ...(query && query.length >= 3 ? {
                OR: [
                  { Material_Number: { contains: query.toLowerCase() } },
                  { Description_Local: { contains: query.toLowerCase() } }
                ]
              } : {})
            }
          }
        },
        select: {
          id: true,
          classCode: true,
          brand: {
            select: {
              name: true
            }
          },
          _count: {
            select: {
              products: {
                where: {
                  isActive: true,
                  ...(query && query.length >= 3 ? {
                    OR: [
                      { Material_Number: { contains: query.toLowerCase() } },
                      { Description_Local: { contains: query.toLowerCase() } }
                    ]
                  } : {})
                }
              }
            }
          }
        },
        orderBy: {
          classCode: 'asc'
        }
      })),

      // Price range
      withRetry(() => prisma.product.aggregate({
        where: {
          isActive: true,
          FinalPrice: { not: null },
          ...(query && query.length >= 3 ? {
            OR: [
              { Material_Number: { contains: query.toLowerCase() } },
              { Description_Local: { contains: query.toLowerCase() } }
            ]
          } : {})
        },
        _min: {
          FinalPrice: true
        },
        _max: {
          FinalPrice: true
        }
      }))
    ])

    // Process products
    const processedProducts: ProductCardInterface[] = products.map(product => ({
      id: product.id,
      Material_Number: product.Material_Number,
      Description_Local: product.Description_Local,
      ImageUrl: product.ImageUrl,
      FinalPrice: toSafeNumber(product.FinalPrice),
      PretAM: toSafeNumber(product.PretAM),
      HasDiscount: product.HasDiscount,
      activeDiscountType: product.activeDiscountType,
      activeDiscountValue: toSafeNumber(product.activeDiscountValue),
      discountPercentage: toSafeNumber(product.discountPercentage),
      categoryLevel3: product.categoryLevel3,
      productClass: product.productClass
    }))

    // Process categories
    const processedCategories = categories.map(cat => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      productCount: cat._count.products
    }))

    // Process brands
    const processedBrands = brands_data.map(brand => ({
      id: brand.id,
      name: brand.name,
      productCount: brand._count.productClasses
    }))

    // Process classes
    const processedClasses = classes_data.map(cls => ({
      id: cls.id,
      name: `${cls.brand.name} - ${cls.classCode}`,
      productCount: cls._count.products
    }))

    // Calculate pagination
    const totalPages = Math.ceil(totalCount / pageSize)

    return {
      products: processedProducts,
      categories: processedCategories,
      brands: processedBrands,
      classes: processedClasses,
      priceRange: {
        min: toSafeNumber(priceRange._min.FinalPrice) || 0,
        max: toSafeNumber(priceRange._max.FinalPrice) || 1000
      },
      pagination: {
        total: totalCount,
        pages: totalPages,
        currentPage: page,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      appliedFilters: filters
    }

  } catch (error) {
    logger.error('Error fetching search page data:', error)

    // Return empty results on error
    return {
      products: [],
      categories: [],
      brands: [],
      classes: [],
      priceRange: { min: 0, max: 1000 },
      pagination: {
        total: 0,
        pages: 0,
        currentPage: 1,
        hasNext: false,
        hasPrev: false
      },
      appliedFilters: filters
    }
  }
}