"use client"

import { useState, useEffect, useCallback } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { ScrollArea } from "@/components/ui/scroll-area"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Check, Filter, X } from "lucide-react"
import { SearchFilters as SearchFiltersType } from "@/types/search"

interface SearchFiltersProps {
  categories: Array<{
    id: string
    name: string
    slug: string | null
    productCount: number
  }>
  brands: Array<{
    id: string
    name: string
    productCount: number
  }>
  classes: Array<{
    id: string
    name: string
    productCount: number
    brandId?: string
    originalClassId?: string
  }>
  priceRange: {
    min: number
    max: number
  }
  appliedFilters: SearchFiltersType
}

export default function SearchFilters({ 
  categories, 
  brands, 
  classes, 
  priceRange, 
  appliedFilters 
}: SearchFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [priceValues, setPriceValues] = useState([
    appliedFilters.minPrice || priceRange.min,
    appliedFilters.maxPrice || priceRange.max
  ])

  const updateFilters = useCallback((updates: Record<string, string | string[] | boolean | undefined>) => {
    const params = new URLSearchParams(searchParams.toString())

    Object.entries(updates).forEach(([key, value]) => {
      if (value === undefined || value === '' || (Array.isArray(value) && value.length === 0)) {
        params.delete(key)
      } else if (Array.isArray(value)) {
        params.set(key, value.join(','))
      } else {
        params.set(key, value.toString())
      }
    })

    params.delete('page') // Reset to first page when filtering
    router.push(`/search?${params.toString()}`)
  }, [router, searchParams])

  const handleCategoryChange = (categoryId: string) => {
    updateFilters({ category3: categoryId === appliedFilters.category3 ? undefined : categoryId })
  }

  const handleBrandToggle = (brandId: string) => {
    const currentBrands = appliedFilters.brands || []
    const newBrands = currentBrands.includes(brandId)
      ? currentBrands.filter(id => id !== brandId)
      : [...currentBrands, brandId]
    
    updateFilters({ brands: newBrands })
  }

  const handleClassToggle = (classId: string) => {
    // Find the class to get the original class ID
    const selectedClass = filteredClasses.find(cls => cls.id === classId)
    const originalClassId = selectedClass?.originalClassId || classId

    const currentClasses = appliedFilters.classes || []
    const newClasses = currentClasses.includes(originalClassId)
      ? currentClasses.filter(id => id !== originalClassId)
      : [...currentClasses, originalClassId]

    updateFilters({ classes: newClasses })
  }

  // Debounced price filter function
  const debouncedPriceFilter = useCallback((values: number[]) => {
    updateFilters({
      minPrice: values[0] === priceRange.min ? undefined : values[0].toString(),
      maxPrice: values[1] === priceRange.max ? undefined : values[1].toString()
    })
  }, [priceRange.min, priceRange.max, updateFilters])

  // Debounce effect for price changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      debouncedPriceFilter(priceValues)
    }, 500) // 500ms debounce delay

    return () => clearTimeout(timeoutId)
  }, [priceValues, debouncedPriceFilter])

  const handlePriceChange = (values: number[]) => {
    setPriceValues(values)
  }

  const handleDiscountToggle = (checked: boolean) => {
    updateFilters({ hasDiscount: checked ? 'true' : undefined })
  }

  const clearAllFilters = () => {
    const params = new URLSearchParams()
    const query = searchParams.get('query')
    if (query) params.set('query', query)
    router.push(`/search?${params.toString()}`)
  }

  const hasActiveFilters = !!(
    appliedFilters.category3 ||
    appliedFilters.brands?.length ||
    appliedFilters.classes?.length ||
    appliedFilters.minPrice ||
    appliedFilters.maxPrice ||
    appliedFilters.hasDiscount
  )

  // Filter classes based on selected brands
  const filteredClasses = classes.filter(cls => {
    // If no brands are selected, show all classes
    if (!appliedFilters.brands?.length) {
      return true
    }
    // If brands are selected, only show classes that belong to selected brands
    return appliedFilters.brands.includes(cls.brandId || '')
  })

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-gray-600 dark:text-gray-400" />
          <h3 className="font-semibold text-gray-900 dark:text-gray-100">Filtre</h3>
        </div>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <X className="h-4 w-4 mr-1" />
            Sterge toate
          </Button>
        )}
      </div>

      {/* Categories */}
      {categories.length > 0 && (
        <div>
          <Label className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 block">
            Categorii
          </Label>
          <RadioGroup
            value={appliedFilters.category3 || ''}
            onValueChange={handleCategoryChange}
          >
            <ScrollArea className="h-48">
              <div className="space-y-2">
                {categories.map((category) => (
                  <div key={category.id} className="flex items-center space-x-2">
                    <RadioGroupItem value={category.id} id={`cat-${category.id}`} />
                    <Label
                      htmlFor={`cat-${category.id}`}
                      className="flex-1 text-sm cursor-pointer flex justify-between"
                    >
                      <span className="truncate">{category.name}</span>
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {category.productCount}
                      </Badge>
                    </Label>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </RadioGroup>
        </div>
      )}

      {/* Brands */}
      {brands.length > 0 && (
        <div>
          <Label className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 block">
            Branduri
          </Label>
          <div className="border rounded-md">
            <Command>
              <CommandInput placeholder="Cauta brand..." />
              <CommandEmpty>Nu s-au gasit branduri.</CommandEmpty>
              <CommandList>
                <CommandGroup>
                  <ScrollArea className="h-48">
                    {brands.map((brand) => (
                      <CommandItem
                        key={brand.id}
                        onSelect={() => handleBrandToggle(brand.id)}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          checked={appliedFilters.brands?.includes(brand.id) || false}
                          onChange={() => handleBrandToggle(brand.id)}
                        />
                        <span className="flex-1">{brand.name}</span>
                        <Badge variant="secondary" className="text-xs">
                          {brand.productCount}
                        </Badge>
                      </CommandItem>
                    ))}
                  </ScrollArea>
                </CommandGroup>
              </CommandList>
            </Command>
          </div>
        </div>
      )}

      {/* Classes */}
      {filteredClasses.length > 0 && (
        <div>
          <Label className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 block">
            Clase produse
          </Label>
          <div className="border rounded-md">
            <Command>
              <CommandInput placeholder="Cauta clasa..." />
              <CommandEmpty>Nu s-au gasit clase.</CommandEmpty>
              <CommandList>
                <CommandGroup>
                  <ScrollArea className="h-48">
                    {filteredClasses.map((cls) => (
                      <CommandItem
                        key={cls.id}
                        onSelect={() => handleClassToggle(cls.id)}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          checked={appliedFilters.classes?.includes(cls.originalClassId || cls.id) || false}
                          onChange={() => handleClassToggle(cls.id)}
                        />
                        <span className="flex-1 text-sm">{cls.name}</span>
                        <Badge variant="secondary" className="text-xs">
                          {cls.productCount}
                        </Badge>
                      </CommandItem>
                    ))}
                  </ScrollArea>
                </CommandGroup>
              </CommandList>
            </Command>
          </div>
        </div>
      )}

      {/* Price Range */}
      <div>
        <Label className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 block">
          Interval preturi
        </Label>
        <div className="space-y-4">
          <Slider
            value={priceValues}
            onValueChange={handlePriceChange}
            max={priceRange.max}
            min={priceRange.min}
            step={1}
            className="w-full"
          />
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
            <span>{priceValues[0]} RON</span>
            <span>{priceValues[1]} RON</span>
          </div>
        </div>
      </div>

      {/* Discount Filter */}
      <div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="discount"
            checked={appliedFilters.hasDiscount || false}
            onCheckedChange={handleDiscountToggle}
          />
          <Label
            htmlFor="discount"
            className="text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer"
          >
            Doar produse cu discount
          </Label>
        </div>
      </div>
    </div>
  )
}
